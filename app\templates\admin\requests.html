{% extends "base.html" %}

{% block title %}إدارة الطلبات - CMSVS{% endblock %}

{% block content %}

<style>
/* Mobile-First Professional Admin Requests Styling */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 12px;
    background: #f8fafc;
    min-height: 100vh;
}

@media (min-width: 640px) {
    .page-container {
        padding: 16px;
    }
}

@media (min-width: 768px) {
    .page-container {
        padding: 24px;
    }
}

.page-header {
    text-align: right;
    padding: 16px;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
    .page-header {
        padding: 24px;
        margin-bottom: 24px;
        border-radius: 12px;
    }
}

@media (min-width: 768px) {
    .page-header {
        padding: 32px 24px;
        margin-bottom: 32px;
    }
}

.page-title {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 6px 0;
    color: #1f2937;
}

@media (min-width: 640px) {
    .page-title {
        font-size: 28px;
        margin: 0 0 8px 0;
    }
}

@media (min-width: 768px) {
    .page-title {
        font-size: 32px;
    }
}

.page-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

@media (min-width: 640px) {
    .page-subtitle {
        font-size: 16px;
    }
}

.btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
    min-height: 44px;
    justify-content: center;
}

@media (min-width: 640px) {
    .btn {
        padding: 10px 16px;
        gap: 6px;
        min-height: auto;
    }
}

.btn-secondary {
    background: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background: #f9fafb;
}

/* Dropdown positioning fixes */
.table-dropdown {
    position: relative;
}

.table-dropdown .dropdown-menu {
    position: fixed !important;
    z-index: 999999 !important;
    min-width: 14rem;
    max-width: 16rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #e5e7eb;
    background: #ffffff !important;
    border-radius: 8px;
}

/* Support for upward dropdown positioning */
.table-dropdown .dropdown-menu.mb-2 {
    margin-bottom: 0.5rem;
    margin-top: 0;
}

.table-dropdown .dropdown-menu.mt-2 {
    margin-top: 0.5rem;
    margin-bottom: 0;
}

/* Prevent horizontal scroll when dropdown is open but allow vertical overflow */
.table-container {
    position: relative;
    overflow-x: auto;
    overflow-y: visible;
}

/* Ensure table rows can contain positioned dropdowns */
.table tbody tr {
    position: relative;
    z-index: 1;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

/* Disable hover effects when dropdown is open */
.table tbody tr:hover:has(.dropdown-menu:not(.hidden)) {
    background-color: transparent;
    transform: none !important;
    box-shadow: none !important;
}

/* Ensure table cells don't interfere with open dropdowns */
.table tbody td:hover:has(.dropdown-menu:not(.hidden)) {
    background-color: transparent;
    transform: none !important;
    box-shadow: none !important;
    z-index: 1 !important;
}

/* Simplified z-index management for dropdowns */
.table tbody tr {
    position: relative;
    z-index: 1;
}

.table tbody td {
    position: relative;
    z-index: 1;
}

/* Dropdown container positioning */
.table-dropdown {
    position: relative;
    z-index: 10;
}

/* When dropdown is open, ensure the containing row has higher z-index */
.table tbody tr:has(.dropdown-menu:not(.hidden)) {
    z-index: 1000 !important;
    position: relative !important;
}

/* Ensure the dropdown cell has proper z-index */
.table tbody td:has(.dropdown-menu:not(.hidden)) {
    z-index: 1000 !important;
    position: relative !important;
}

/* Ensure dropdown container gets highest z-index when open */
.table-dropdown:has(.dropdown-menu:not(.hidden)) {
    z-index: 1001 !important;
    position: relative !important;
}

/* Ensure card body allows overflow for dropdowns */
.card-body {
    overflow: visible !important;
}

/* Ensure card container allows overflow for dropdowns */
.card {
    overflow: visible !important;
}

/* Ensure main content area allows overflow */
.main-content {
    overflow: visible !important;
}

/* Mobile-First Card Styles */
.card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 16px;
    overflow: visible !important;
}

@media (min-width: 640px) {
    .card {
        border-radius: 12px;
        margin-bottom: 20px;
    }
}

@media (min-width: 768px) {
    .card {
        margin-bottom: 24px;
    }
}

.card-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

@media (min-width: 640px) {
    .card-header {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-header {
        padding: 20px 24px;
    }
}

.card-body {
    padding: 12px 16px;
}

@media (min-width: 640px) {
    .card-body {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-body {
        padding: 20px 24px;
    }
}

.card-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

@media (min-width: 640px) {
    .card-footer {
        padding: 16px 20px;
    }
}

@media (min-width: 768px) {
    .card-footer {
        padding: 20px 24px;
    }
}

/* Mobile-First Form Styles */
.form-group {
    margin-bottom: 16px;
}

@media (min-width: 640px) {
    .form-group {
        margin-bottom: 20px;
    }
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
}

@media (min-width: 640px) {
    .form-label {
        font-size: 16px;
        margin-bottom: 8px;
    }
}

.form-input,
.form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: #ffffff;
    transition: border-color 0.2s, box-shadow 0.2s;
    min-height: 44px;
}

@media (min-width: 640px) {
    .form-input,
    .form-select {
        padding: 12px 16px;
        font-size: 16px;
        min-height: auto;
    }
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-First Table Styles */
.table-container {
    position: relative;
    overflow-x: auto;
    overflow-y: visible !important;
    -webkit-overflow-scrolling: touch;
    z-index: 1;
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
    min-width: 800px;
    position: relative;
    z-index: 1;
}

/* Ensure dropdowns are not clipped by table container */
.table-container:has(.dropdown-menu.show) {
    overflow: visible !important;
}

@media (min-width: 768px) {
    .table {
        font-size: 16px;
    }
}

.table-header {
    background-color: #f9fafb;
}

.table-header-cell {
    padding: 8px 12px;
    text-align: right;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (min-width: 640px) {
    .table-header-cell {
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (min-width: 768px) {
    .table-header-cell {
        padding: 16px;
    }
}

.table-body {
    background-color: #ffffff;
}

.table-cell {
    padding: 8px 12px;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: top;
    word-wrap: break-word;
    overflow: hidden;
}

@media (min-width: 640px) {
    .table-cell {
        padding: 12px 16px;
    }
}

@media (min-width: 768px) {
    .table-cell {
        padding: 16px;
    }
}

/* Mobile Card View Styles */
.mobile-table-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
    .mobile-table-card {
        padding: 20px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
}

.mobile-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 4px;
}

@media (min-width: 640px) {
    .mobile-title {
        font-size: 18px;
        margin-bottom: 6px;
    }
}

.mobile-table-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 4px 0;
}

@media (min-width: 640px) {
    .mobile-table-row {
        padding: 6px 0;
    }
}

.mobile-table-label {
    font-size: 12px;
    font-weight: 500;
    color: #6b7280;
    flex-shrink: 0;
    width: 100px;
}

@media (min-width: 640px) {
    .mobile-table-label {
        font-size: 14px;
        width: 120px;
    }
}

.mobile-table-value {
    font-size: 14px;
    color: #1f2937;
    text-align: left;
    flex: 1;
}

@media (min-width: 640px) {
    .mobile-table-value {
        font-size: 16px;
    }
}

/* Enhanced mobile responsive dropdown */
@media (max-width: 767px) {
    .table-dropdown .dropdown-menu {
        min-width: 240px !important;
        max-width: 280px !important;
        font-size: 14px;
        position: fixed !important;
        z-index: 999999 !important;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25), 0 10px 20px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        background: #ffffff !important;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Enhanced mobile dropdown items */
    .dropdown-item {
        padding: 16px 20px !important;
        font-size: 15px !important;
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        touch-action: manipulation;
    }

    /* Larger touch targets for mobile */
    .dropdown-trigger {
        width: 44px !important;
        height: 44px !important;
        min-width: 44px !important;
        min-height: 44px !important;
        touch-action: manipulation;
    }

    /* Mobile-specific hover states (touch devices) */
    .dropdown-item:active {
        background-color: #f3f4f6 !important;
        transform: scale(0.98);
    }

    /* Ensure table doesn't interfere on mobile */
    .table-container {
        overflow: visible !important;
    }

    /* Enhanced mobile dropdown animation */
    .dropdown-menu {
        transform: translateY(-20px) scale(0.9);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dropdown-menu.dropdown-entered {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    /* Mobile-specific positioning adjustments */
    .dropdown-menu.positioned-below {
        margin-top: 4px;
    }

    .dropdown-menu.positioned-above {
        margin-bottom: 4px;
    }

    /* Enhanced mobile touch feedback */
    .dropdown-item:active {
        background-color: #e5e7eb !important;
        transform: scale(0.98) translateX(-2px);
    }

    /* Mobile keyboard navigation improvements */
    .dropdown-item.keyboard-focused {
        background-color: #dbeafe !important;
        border-left: 4px solid #3b82f6;
    }
}

/* Tablet-specific optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    .table-dropdown .dropdown-menu {
        min-width: 220px;
        font-size: 14px;
    }

    .dropdown-item {
        padding: 14px 18px;
        min-height: 44px;
    }

    .dropdown-trigger {
        width: 36px;
        height: 36px;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .dropdown-menu {
        border-width: 0.5px;
    }

    .dropdown-item {
        border-bottom-width: 0.5px;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .dropdown-menu,
    .dropdown-item,
    .dropdown-trigger {
        transition: none !important;
        animation: none !important;
    }

    .dropdown-menu.dropdown-entering,
    .dropdown-menu.dropdown-entered,
    .dropdown-menu.dropdown-exiting {
        transform: none !important;
        opacity: 1 !important;
    }
}

/* Advanced theming system with CSS custom properties */
:root {
    --dropdown-bg: #ffffff;
    --dropdown-border: #e5e7eb;
    --dropdown-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    --dropdown-text: #374151;
    --dropdown-text-muted: #6b7280;
    --dropdown-hover-bg: #f3f4f6;
    --dropdown-focus-ring: #3b82f6;
    --dropdown-border-radius: 12px;
    --dropdown-animation-duration: 0.25s;
    --dropdown-animation-easing: cubic-bezier(0.4, 0, 0.2, 1);

    /* Status-specific colors */
    --status-pending-color: #eab308;
    --status-pending-bg: #fefce8;
    --status-progress-color: #3b82f6;
    --status-progress-bg: #eff6ff;
    --status-completed-color: #22c55e;
    --status-completed-bg: #f0fdf4;
    --status-rejected-color: #ef4444;
    --status-rejected-bg: #fef2f2;
}

/* Dark theme variables */
[data-theme="dark"] {
    --dropdown-bg: #1f2937;
    --dropdown-border: #374151;
    --dropdown-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    --dropdown-text: #f9fafb;
    --dropdown-text-muted: #d1d5db;
    --dropdown-hover-bg: #374151;
    --dropdown-focus-ring: #60a5fa;

    --status-pending-bg: rgba(234, 179, 8, 0.1);
    --status-progress-bg: rgba(59, 130, 246, 0.1);
    --status-completed-bg: rgba(34, 197, 94, 0.1);
    --status-rejected-bg: rgba(239, 68, 68, 0.1);
}

/* High contrast theme */
[data-theme="high-contrast"] {
    --dropdown-bg: #000000;
    --dropdown-border: #ffffff;
    --dropdown-text: #ffffff;
    --dropdown-hover-bg: #333333;
    --dropdown-focus-ring: #ffff00;
    --dropdown-border-radius: 4px;
}

/* Compact theme */
[data-theme="compact"] {
    --dropdown-border-radius: 6px;
    --dropdown-animation-duration: 0.15s;
}

/* Apply theme variables to dropdown components */
.dropdown-menu {
    background-color: var(--dropdown-bg) !important;
    border-color: var(--dropdown-border) !important;
    color: var(--dropdown-text) !important;
    box-shadow: var(--dropdown-shadow) !important;
    border-radius: var(--dropdown-border-radius) !important;
    transition: all var(--dropdown-animation-duration) var(--dropdown-animation-easing) !important;
}

.dropdown-item {
    color: var(--dropdown-text) !important;
    transition: all var(--dropdown-animation-duration) var(--dropdown-animation-easing) !important;
}

.dropdown-item:hover {
    background-color: var(--dropdown-hover-bg) !important;
}

.dropdown-trigger:focus {
    box-shadow: 0 0 0 2px var(--dropdown-focus-ring) !important;
}

/* Dark mode support with system preference */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        --dropdown-bg: #1f2937;
        --dropdown-border: #374151;
        --dropdown-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        --dropdown-text: #f9fafb;
        --dropdown-text-muted: #d1d5db;
        --dropdown-hover-bg: #374151;
        --dropdown-focus-ring: #60a5fa;
    }
}

    .table-dropdown .dropdown-menu .py-1 {
        padding: 0.5rem 0;
    }

    .table-dropdown .dropdown-menu a,
    .table-dropdown .dropdown-menu button {
        padding: 12px 16px !important;
        font-size: 14px;
        line-height: 1.4;
    }

    .table-dropdown .dropdown-menu i {
        width: 16px;
        margin-left: 8px;
    }
}

@media (min-width: 640px) {
    .table-dropdown .dropdown-menu {
        min-width: 12rem;
        max-width: 14rem;
        font-size: 16px;
    }
}

@media (min-width: 768px) {
    .table-dropdown .dropdown-menu {
        min-width: 14rem;
        max-width: 16rem;
    }
}

/* Force dropdown to be visible when shown with solid background */
.dropdown-menu:not(.hidden) {
    display: block !important;
    z-index: 999999 !important;
    position: fixed !important;
    background-color: #ffffff !important;
    background: #ffffff !important;
    backdrop-filter: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure dropdown is completely opaque and visible */
.dropdown-menu {
    opacity: 1 !important;
    visibility: visible !important;
}

.dropdown-menu.hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Fix any ring opacity conflicts */
.dropdown-menu * {
    opacity: 1 !important;
}

/* Ensure dropdown items are fully visible */
.dropdown-menu a,
.dropdown-menu button,
.dropdown-menu div {
    opacity: 1 !important;
}

/* Ensure parent containers don't clip the dropdown */
.table-container {
    overflow-x: auto;
    overflow-y: visible !important;
    position: relative;
    z-index: 1;
}

.card {
    overflow: visible !important;
    position: relative;
    z-index: 1;
}

.card-body {
    overflow: visible !important;
    position: relative;
    z-index: 1;
}

/* Enhanced dropdown styling */
.table-dropdown .dropdown-menu {
    z-index: 999999 !important;
    background-color: #ffffff !important;
    background: #ffffff !important;
    opacity: 1 !important;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    position: fixed !important;
}

/* Ensure dropdown menu is always on top */
.dropdown-menu:not(.hidden) {
    z-index: 999999 !important;
    position: fixed !important;
}

/* Enhanced dropdown trigger button */
.dropdown-trigger {
    position: relative;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-trigger:hover {
    transform: scale(1.05);
}

.dropdown-trigger:active {
    transform: scale(0.95);
}

.dropdown-trigger:focus {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Enhanced dropdown items */
.dropdown-item {
    position: relative;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-item:hover {
    transform: translateX(-2px);
}

.dropdown-item:focus {
    outline: none;
    box-shadow: inset 2px 0 0 0 currentColor;
}

/* Status-specific hover colors */
.dropdown-item:hover.hover\:bg-yellow-50 {
    background-color: #fefce8 !important;
    border-left: 3px solid #eab308;
}

.dropdown-item:hover.hover\:bg-blue-50 {
    background-color: #eff6ff !important;
    border-left: 3px solid #3b82f6;
}

.dropdown-item:hover.hover\:bg-green-50 {
    background-color: #f0fdf4 !important;
    border-left: 3px solid #22c55e;
}

.dropdown-item:hover.hover\:bg-red-50 {
    background-color: #fef2f2 !important;
    border-left: 3px solid #ef4444;
}

/* Advanced dropdown animation system */
.dropdown-menu {
    transform: translateY(-10px) scale(0.95);
    opacity: 0;
    visibility: hidden;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top center;
    will-change: transform, opacity;
}

.dropdown-menu.dropdown-entering {
    transform: translateY(-15px) scale(0.9);
    opacity: 0;
}

.dropdown-menu.dropdown-entered {
    transform: translateY(0) scale(1);
    opacity: 1;
    visibility: visible;
}

.dropdown-menu.dropdown-exiting {
    transform: translateY(-5px) scale(0.98);
    opacity: 0;
    transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

/* Staggered animation for dropdown items */
.dropdown-item {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-menu.dropdown-entered .dropdown-item {
    opacity: 1;
    transform: translateX(0);
}

.dropdown-menu.dropdown-entered .dropdown-item:nth-child(1) { transition-delay: 0.05s; }
.dropdown-menu.dropdown-entered .dropdown-item:nth-child(2) { transition-delay: 0.1s; }
.dropdown-menu.dropdown-entered .dropdown-item:nth-child(3) { transition-delay: 0.15s; }
.dropdown-menu.dropdown-entered .dropdown-item:nth-child(4) { transition-delay: 0.2s; }
.dropdown-menu.dropdown-entered .dropdown-item:nth-child(5) { transition-delay: 0.25s; }
.dropdown-menu.dropdown-entered .dropdown-item:nth-child(6) { transition-delay: 0.3s; }

/* Enhanced button animation */
.dropdown-trigger {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, background-color;
}

.dropdown-trigger.active {
    background-color: #f3f4f6;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dropdown-trigger.active svg {
    transform: rotate(90deg);
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading state for dropdown trigger */
.dropdown-trigger.loading {
    pointer-events: none;
    opacity: 0.7;
}

.dropdown-trigger.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    margin: -6px 0 0 -6px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced keyboard focus styling */
.dropdown-item.keyboard-focused {
    background-color: #f3f4f6 !important;
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

/* Status action loading states */
.status-action.loading {
    opacity: 0.7;
    pointer-events: none;
}

.status-loader {
    display: inline-flex;
    align-items: center;
}

/* Positioning classes for different dropdown orientations */
.dropdown-menu.positioned-above {
    animation: slideUpFadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-menu.positioned-below {
    animation: slideDownFadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-menu.positioned-above-constrained,
.dropdown-menu.positioned-below-constrained {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
}

.dropdown-menu.positioned-above-constrained::-webkit-scrollbar,
.dropdown-menu.positioned-below-constrained::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu.positioned-above-constrained::-webkit-scrollbar-track,
.dropdown-menu.positioned-below-constrained::-webkit-scrollbar-track {
    background: #f9fafb;
    border-radius: 3px;
}

.dropdown-menu.positioned-above-constrained::-webkit-scrollbar-thumb,
.dropdown-menu.positioned-below-constrained::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.dropdown-menu.positioned-above-constrained::-webkit-scrollbar-thumb:hover,
.dropdown-menu.positioned-below-constrained::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

@keyframes slideDownFadeIn {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideUpFadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Advanced visual effects and micro-interactions */
.dropdown-trigger {
    position: relative;
    overflow: hidden;
}

.dropdown-trigger::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.dropdown-trigger:active::before {
    width: 100px;
    height: 100px;
}

/* Advanced loading states */
.dropdown-trigger.loading::after {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse animation for notifications */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.dropdown-item.has-notification {
    animation: pulse 2s infinite;
}

/* Shimmer effect for loading states */
@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.dropdown-item.loading {
    background: linear-gradient(90deg, #f0f0f0 0px, #e0e0e0 40px, #f0f0f0 80px);
    background-size: 200px;
    animation: shimmer 1.5s infinite;
}

/* Advanced focus indicators */
.dropdown-item:focus-visible {
    outline: 2px solid var(--dropdown-focus-ring);
    outline-offset: -2px;
    box-shadow: inset 0 0 0 2px rgba(255, 255, 255, 0.2);
}

/* Smooth color transitions */
.dropdown-item {
    transition:
        background-color var(--dropdown-animation-duration) var(--dropdown-animation-easing),
        color var(--dropdown-animation-duration) var(--dropdown-animation-easing),
        transform var(--dropdown-animation-duration) var(--dropdown-animation-easing),
        box-shadow var(--dropdown-animation-duration) var(--dropdown-animation-easing);
}

/* Touch feedback */
.touch-active {
    transform: scale(0.98);
    background-color: var(--dropdown-hover-bg) !important;
}

/* Status-specific animations */
.status-action[data-status="pending"] {
    --status-color: var(--status-pending-color);
    --status-bg: var(--status-pending-bg);
}

.status-action[data-status="in_progress"] {
    --status-color: var(--status-progress-color);
    --status-bg: var(--status-progress-bg);
}

.status-action[data-status="completed"] {
    --status-color: var(--status-completed-color);
    --status-bg: var(--status-completed-bg);
}

.status-action[data-status="rejected"] {
    --status-color: var(--status-rejected-color);
    --status-bg: var(--status-rejected-bg);
}

.status-action:hover {
    background-color: var(--status-bg) !important;
    color: var(--status-color) !important;
}

/* Advanced positioning classes */
.dropdown-menu[data-position="top-left"] {
    transform-origin: bottom left;
}

.dropdown-menu[data-position="top-right"] {
    transform-origin: bottom right;
}

.dropdown-menu[data-position="bottom-left"] {
    transform-origin: top left;
}

.dropdown-menu[data-position="bottom-right"] {
    transform-origin: top right;
}

/* Container query support (future-proofing) */
@supports (container-type: inline-size) {
    .table-container {
        container-type: inline-size;
    }

    @container (max-width: 768px) {
        .dropdown-menu {
            width: 90vw;
            max-width: 280px;
        }
    }
}

/* Advanced scrollbar styling */
.dropdown-menu::-webkit-scrollbar {
    width: 8px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: var(--dropdown-bg);
    border-radius: 4px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--dropdown-border);
    border-radius: 4px;
    border: 2px solid var(--dropdown-bg);
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: var(--dropdown-text-muted);
}

/* Print styles */
@media print {
    .dropdown-trigger,
    .dropdown-menu {
        display: none !important;
    }
}

/* Ensure dropdown appears above everything with solid background */
.dropdown-menu {
    z-index: 999999 !important;
    background-color: #ffffff !important;
    background: #ffffff !important;
    opacity: 1 !important;
}

/* Ensure table rows don't interfere with dropdowns */
.table tbody tr {
    position: relative;
    z-index: 1;
}

/* When dropdown is open, ensure proper layering */
.table-dropdown.show {
    z-index: 999998 !important;
    position: relative;
}

/* Toast notification styles */
.toast-notification {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    font-weight: 500;
    max-width: 300px;
    word-wrap: break-word;
}

/* Button loading state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none;
}

/* Improved action button styles */
.btn-success {
    background-color: #10b981;
    color: white;
    border: 1px solid #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    border: 1px solid #ef4444;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Mobile action buttons */
@media (max-width: 767px) {
    .btn-success,
    .btn-danger {
        min-width: 80px;
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* Search section toggle animation */
#searchSectionContent {
    transition: all 0.3s ease-in-out;
}

#searchToggleIcon {
    transition: transform 0.2s ease-in-out;
}

/* Mobile-First Badge Styles */
.badge-warning,
.badge-info,
.badge-success,
.badge-danger,
.badge-gray {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

@media (min-width: 640px) {
    .badge-warning,
    .badge-info,
    .badge-success,
    .badge-danger,
    .badge-gray {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 16px;
    }
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-gray {
    background-color: #f3f4f6;
    color: #374151;
}

/* Quick info badges */
.filter-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Mobile-First Button Variants */
.btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
    border: 1px solid #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
    border: 1px solid #6b7280;
}

.btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

.btn-success {
    background-color: #10b981;
    color: #ffffff;
    border: 1px solid #10b981;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: #ffffff;
    border: 1px solid #ef4444;
}

.btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}
</style>

<div class="page-container">
    <!-- Page Header -->
    <header class="page-header">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-5">
            <div class="flex-1">
                <h1 class="page-title">إدارة الطلبات</h1>
                <p class="page-subtitle">إدارة ومراقبة جميع طلبات النظام</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <a href="/admin/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i>
                    العودة للوحة التحكم
                </a>
                <a href="/requests/new" class="btn btn-outline">
                    <i class="fas fa-plus"></i>
                    إنشاء طلب جديد
                </a>
            </div>
        </div>
    </header>

    <!-- New Request Button -->
    <div class="flex justify-end mb-4">
        <a href="/requests/new" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            إنشاء طلب جديد
        </a>
    </div>

    <!-- Search Section -->
    <div class="card">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <h5 class="text-lg font-semibold text-gray-900">البحث والتصفية</h5>
                <button type="button" onclick="toggleSearchSection()" class="{% if current_search or current_status %}text-blue-600 hover:text-blue-700{% else %}text-gray-500 hover:text-gray-700{% endif %} focus:outline-none p-1 rounded-md hover:bg-gray-100" id="searchToggleBtn">
                    {% if current_search or current_status %}
                    <div class="flex items-center space-x-1 rtl:space-x-reverse">
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <svg class="w-5 h-5 transform transition-transform duration-200" id="searchToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                    {% else %}
                    <svg class="w-5 h-5 transform transition-transform duration-200" id="searchToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    {% endif %}
                </button>
            </div>
            <!-- Quick info when collapsed -->
            <div id="searchQuickInfo" class="mt-2 text-sm text-gray-600 hidden">
                {% if current_search or current_status %}
                <div class="flex flex-wrap gap-2">
                    {% if current_search %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        بحث: {{ current_search }}
                    </span>
                    {% endif %}
                    {% if current_status %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        الحالة:
                        {% if current_status == 'pending' %}قيد المراجعة
                        {% elif current_status == 'in_progress' %}قيد التنفيذ
                        {% elif current_status == 'completed' %}مكتملة
                        {% elif current_status == 'rejected' %}مرفوضة
                        {% endif %}
                    </span>
                    {% endif %}
                </div>
                {% endif %}
                <div class="mt-1">
                    <span class="font-medium">{{ total_requests }} طلب إجمالي</span>
                    {% if total_pages > 1 %}
                    - الصفحة {{ current_page }} من {{ total_pages }}
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body" id="searchSectionContent">
            <form method="get" action="/admin/requests" id="searchForm">
                <!-- Hidden input to reset to page 1 when searching -->
                <input type="hidden" name="page" value="1">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="form-group">
                        <label class="form-label">البحث في الطلبات</label>
                        <div class="relative">
                            <input type="text" name="search" value="{{ current_search or '' }}" class="form-input pl-10" placeholder="ابحث برقم الطلب أو اسم المستخدم...">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">تصفية بالحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for status in statuses %}
                            <option value="{{ status }}" {% if current_status == status %}selected{% endif %}>
                                {% if status == 'pending' %}قيد المراجعة
                                {% elif status == 'in_progress' %}قيد التنفيذ
                                {% elif status == 'completed' %}مكتملة
                                {% elif status == 'rejected' %}مرفوضة
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">عدد النتائج</label>
                        <select name="per_page" class="form-select">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label hidden sm:block">&nbsp;</label>
                        <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                            <button type="submit" class="btn btn-secondary w-full sm:w-auto">بحث</button>
                            <a href="/admin/requests" class="btn btn-outline w-full sm:w-auto">مسح</a>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    {% if current_search %}
                    <small class="text-gray-600">
                        نتائج البحث: "{{ current_search }}"<br>
                        <span class="font-medium">{{ total_requests }} نتيجة إجمالية</span>
                        {% if total_pages > 1 %}
                        - الصفحة {{ current_page }} من {{ total_pages }}
                        {% endif %}
                    </small>
                    {% else %}
                    <small class="text-gray-600">
                        <span class="font-medium">{{ total_requests }} طلب إجمالي</span>
                        {% if total_pages > 1 %}
                        - الصفحة {{ current_page }} من {{ total_pages }}
                        {% endif %}
                    </small>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Status Filter Tabs -->
    <div class="card">
        <div class="card-body">
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-wrap gap-2">
                <a href="/admin/requests" class="btn btn-secondary {% if not current_status %}btn-primary{% endif %} text-center">
                    الكل
                </a>
                {% for status in statuses %}
                <a href="/admin/requests?status={{ status }}" class="btn btn-secondary {% if current_status == status %}btn-primary{% endif %} text-center">
                    {% if status == 'pending' %}قيد المراجعة
                    {% elif status == 'in_progress' %}قيد التنفيذ
                    {% elif status == 'completed' %}مكتملة
                    {% elif status == 'rejected' %}مرفوضة
                    {% endif %}
                </a>
                {% endfor %}
                <a href="/admin/archive" class="btn btn-secondary text-center col-span-2 sm:col-span-1">
                    الطلبات المؤرشفة
                </a>
            </div>
        </div>
    </div>

    {% if requests %}

    <!-- Requests Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="text-lg font-semibold text-gray-900">قائمة الطلبات</h5>
        </div>
        <div class="card-body p-0">
            <!-- Desktop Table View -->
            <div class="hidden lg:block table-container overflow-x-auto relative">
                <table class="table">
                    <thead class="table-header">
                        <tr>
                            <th class="table-header-cell" style="width: 12%;">رقم الطلب</th>
                            <th class="table-header-cell" style="width: 15%;">الرمز التعريفي</th>
                            <th class="table-header-cell" style="width: 12%;">إجازة البناء</th>
                            <th class="table-header-cell" style="width: 20%;">الإسم الثلاثي</th>
                            <th class="table-header-cell" style="width: 10%;">الحالة</th>
                            <th class="table-header-cell" style="width: 8%;">المرفقات</th>
                            <th class="table-header-cell" style="width: 13%;">تاريخ الإنشاء</th>
                            <th class="table-header-cell" style="width: 10%;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        {% for req in requests %}
                        <tr>
                            <td class="table-cell">
                                <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <code class="text-xs bg-blue-100 px-2 py-1 rounded text-blue-800">{{ req.unique_code }}</code>
                                    <button onclick="copyToClipboard('{{ req.unique_code }}')" class="text-gray-400 hover:text-gray-600" title="نسخ الرمز">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td class="table-cell">
                                {% if req.building_permit_number %}
                                <code class="text-xs bg-green-100 px-2 py-1 rounded text-green-800">{{ req.building_permit_number }}</code>
                                {% else %}
                                <span class="text-gray-400 text-sm">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-xs font-medium ml-3">
                                        {{ req.full_name[0] if req.full_name else 'م' }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ req.full_name or 'غير محدد' }}</div>
                                        <div class="text-xs text-gray-500">{{ req.user.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="table-cell">
                                {% if req.status.value == 'pending' %}
                                <span class="badge-warning">قيد المراجعة</span>
                                {% elif req.status.value == 'in_progress' %}
                                <span class="badge-info">قيد التنفيذ</span>
                                {% elif req.status.value == 'completed' %}
                                <span class="badge-success">مكتمل</span>
                                {% elif req.status.value == 'rejected' %}
                                <span class="badge-danger">مرفوض</span>
                                {% endif %}
                            </td>
                            <td class="table-cell">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-gray-400 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                    </svg>
                                    <span class="text-sm text-gray-600">{{ req.files|length }}</span>
                                </div>
                            </td>
                            <td class="table-cell">
                                <div class="text-sm text-gray-900">{{ req.created_at.strftime('%Y-%m-%d') }}</div>
                                <div class="text-xs text-gray-500">{{ req.created_at.strftime('%H:%M') }}</div>
                            </td>
                            <td class="table-cell">
                                <div class="flex space-x-2 rtl:space-x-reverse">
                                    <a href="/requests/{{ req.id }}" class="text-primary-600 hover:text-primary-500 text-sm">
                                        عرض
                                    </a>
                                    <div class="table-dropdown relative inline-block text-left">
                                        <button type="button"
                                                class="dropdown-trigger inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                                                onclick="toggleDropdown({{ req.id }}, event)"
                                                aria-label="إجراءات الطلب رقم {{ req.request_number }} - الحالة الحالية: {{ req.status.value|title }}"
                                                aria-haspopup="true"
                                                aria-expanded="false"
                                                aria-describedby="dropdown-help-{{ req.id }}"
                                                data-request-id="{{ req.id }}"
                                                data-request-status="{{ req.status.value }}"
                                                data-request-number="{{ req.request_number }}"
                                                title="اضغط Enter أو Space لفتح قائمة الإجراءات">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" role="img">
                                                <title>أيقونة قائمة الإجراءات</title>
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                            </svg>
                                        </button>

                                        <!-- Hidden accessibility helper -->
                                        <div id="dropdown-help-{{ req.id }}" class="sr-only">
                                            استخدم الأسهم للتنقل، Enter للاختيار، Escape للإغلاق. يمكنك أيضاً كتابة أول حرف من الخيار للانتقال إليه مباشرة.
                                        </div>
                                        <div id="dropdown-{{ req.id }}"
                                             class="dropdown-menu hidden w-64 rounded-lg shadow-xl bg-white border border-gray-200 ring-1 ring-black ring-opacity-5 focus:outline-none"
                                             role="menu"
                                             aria-orientation="vertical"
                                             aria-labelledby="dropdown-button-{{ req.id }}"
                                             aria-describedby="dropdown-help-{{ req.id }}"
                                             aria-live="polite"
                                             aria-atomic="true"
                                             data-dropdown-type="actions"
                                             data-request-id="{{ req.id }}"
                                             tabindex="-1">
                                            <div class="py-2">
                                                <div class="px-4 py-2 text-xs font-medium text-gray-500 border-b border-gray-100 bg-gray-50">
                                                    <i class="fas fa-cog text-gray-400 mr-2"></i>
                                                    إجراءات الطلب #{{ req.request_number }}
                                                </div>

                                                <!-- Primary Actions Section -->
                                                <div class="py-1">
                                                    <a href="/requests/{{ req.id }}/edit"
                                                       class="dropdown-item group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150"
                                                       role="menuitem"
                                                       tabindex="-1">
                                                        <i class="fas fa-edit text-blue-600 group-hover:text-blue-700 ml-3 w-4 text-center"></i>
                                                        <span class="font-medium">تعديل الطلب</span>
                                                    </a>

                                                    <a href="/requests/{{ req.id }}"
                                                       class="dropdown-item group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-150"
                                                       role="menuitem"
                                                       tabindex="-1">
                                                        <i class="fas fa-eye text-green-600 group-hover:text-green-700 ml-3 w-4 text-center"></i>
                                                        <span class="font-medium">عرض التفاصيل</span>
                                                    </a>
                                                </div>

                                                <!-- Divider -->
                                                <div class="border-t border-gray-200 my-1"></div>

                                                <!-- Status Change Options -->
                                                <div class="py-1">
                                                    <div class="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50">
                                                        <i class="fas fa-exchange-alt text-gray-400 mr-2"></i>
                                                        تغيير الحالة
                                                    </div>

                                                    {% if req.status.value != 'pending' %}
                                                    <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                        <input type="hidden" name="status" value="pending">
                                                        <button type="submit"
                                                                class="dropdown-item status-action group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-yellow-50 hover:text-yellow-700 transition-all duration-150"
                                                                role="menuitem"
                                                                tabindex="-1"
                                                                data-status="pending"
                                                                data-request-id="{{ req.id }}"
                                                                onclick="handleStatusChange(event, {{ req.id }}, 'pending')">
                                                            <i class="fas fa-clock text-yellow-600 group-hover:text-yellow-700 ml-3 w-4 text-center"></i>
                                                            <span class="font-medium">قيد المراجعة</span>
                                                            <span class="status-loader hidden ml-2">
                                                                <i class="fas fa-spinner fa-spin text-xs"></i>
                                                            </span>
                                                        </button>
                                                    </form>
                                                    {% endif %}

                                                    {% if req.status.value != 'in_progress' %}
                                                    <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                        <input type="hidden" name="status" value="in_progress">
                                                        <button type="submit"
                                                                class="dropdown-item status-action group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-all duration-150"
                                                                role="menuitem"
                                                                tabindex="-1"
                                                                data-status="in_progress"
                                                                data-request-id="{{ req.id }}"
                                                                onclick="handleStatusChange(event, {{ req.id }}, 'in_progress')">
                                                            <i class="fas fa-cogs text-blue-600 group-hover:text-blue-700 ml-3 w-4 text-center"></i>
                                                            <span class="font-medium">قيد التنفيذ</span>
                                                            <span class="status-loader hidden ml-2">
                                                                <i class="fas fa-spinner fa-spin text-xs"></i>
                                                            </span>
                                                        </button>
                                                    </form>
                                                    {% endif %}

                                                    {% if req.status.value != 'completed' %}
                                                    <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                        <input type="hidden" name="status" value="completed">
                                                        <button type="submit"
                                                                class="dropdown-item group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 transition-all duration-150"
                                                                role="menuitem"
                                                                tabindex="-1">
                                                            <i class="fas fa-check-circle text-green-600 group-hover:text-green-700 ml-3 w-4 text-center"></i>
                                                            <span class="font-medium">مكتمل</span>
                                                        </button>
                                                    </form>
                                                    {% endif %}

                                                    {% if req.status.value != 'rejected' %}
                                                    <form method="post" action="/admin/requests/{{ req.id }}/update-status" class="block">
                                                        <input type="hidden" name="status" value="rejected">
                                                        <button type="submit"
                                                                class="dropdown-item group flex items-center w-full text-right px-4 py-3 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 transition-all duration-150"
                                                                role="menuitem"
                                                                tabindex="-1">
                                                            <i class="fas fa-times-circle text-red-600 group-hover:text-red-700 ml-3 w-4 text-center"></i>
                                                            <span class="font-medium">مرفوض</span>
                                                        </button>
                                                    </form>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile Card View -->
            <div class="lg:hidden">
                {% for req in requests %}
                <div class="mobile-table-card border-r-4 border-r-{{ 'green' if req.status.value == 'approved' else 'yellow' if req.status.value == 'pending' else 'red' if req.status.value == 'rejected' else 'blue' }}-500">
                    <!-- Request Header -->
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <div class="mobile-title">{{ req.full_name or 'غير محدد' }}</div>
                            <code class="text-xs bg-gray-100 px-2 py-1 rounded">{{ req.request_number }}</code>
                        </div>
                        <div class="text-right">
                            {% if req.status.value == 'pending' %}
                            <span class="badge-warning">قيد المراجعة</span>
                            {% elif req.status.value == 'approved' %}
                            <span class="badge-success">مقبول</span>
                            {% elif req.status.value == 'rejected' %}
                            <span class="badge-danger">مرفوض</span>
                            {% elif req.status.value == 'in_progress' %}
                            <span class="badge-info">قيد التنفيذ</span>
                            {% else %}
                            <span class="badge-gray">{{ req.status.value }}</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Request Details -->
                    <div class="space-y-2 mb-4">
                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المستخدم:</span>
                            <span class="mobile-table-value">{{ req.user.full_name }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">الرمز التعريفي:</span>
                            <span class="mobile-table-value">{{ req.unique_code or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">إجازة البناء:</span>
                            <span class="mobile-table-value">{{ req.building_permit or 'غير محدد' }}</span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">المرفقات:</span>
                            <span class="mobile-table-value">
                                {% set file_count = req.files|length %}
                                {% if file_count > 0 %}
                                <span class="text-blue-600">{{ file_count }} ملف</span>
                                {% else %}
                                <span class="text-gray-500">لا توجد مرفقات</span>
                                {% endif %}
                            </span>
                        </div>

                        <div class="mobile-table-row">
                            <span class="mobile-table-label">تاريخ الإنشاء:</span>
                            <span class="mobile-table-value">{{ req.created_at.strftime('%Y-%m-%d') }}</span>
                        </div>
                    </div>

                    <!-- Admin Action Buttons -->
                    <div class="flex flex-wrap gap-2">
                        <a href="/admin/requests/{{ req.id }}" class="btn-primary text-xs px-3 py-2">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>

                        <a href="/admin/requests/{{ req.id }}/edit" class="btn-secondary text-xs px-3 py-2">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>

                        {% if req.files %}
                        <a href="/admin/requests/{{ req.id }}/files" class="btn-outline text-xs px-3 py-2">
                            <i class="fas fa-paperclip"></i>
                            المرفقات
                        </a>
                        {% endif %}

                        <!-- Status Update Buttons -->
                        {% if req.status.value == 'pending' %}
                        <button onclick="updateStatus({{ req.id }}, 'approved')" class="btn-success text-xs px-3 py-2">
                            <i class="fas fa-check"></i>
                            قبول
                        </button>
                        <button onclick="updateStatus({{ req.id }}, 'rejected')" class="btn-danger text-xs px-3 py-2">
                            <i class="fas fa-times"></i>
                            رفض
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pagination Controls -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="text-sm text-gray-700 text-center sm:text-right">
                    عرض {{ ((current_page - 1) * per_page) + 1 }} إلى {{ ((current_page - 1) * per_page) + requests|length }} من {{ total_requests }} نتيجة
                </div>
                <div class="flex items-center justify-center sm:justify-end space-x-2 rtl:space-x-reverse overflow-x-auto">
                    <!-- Previous Page -->
                    {% if current_page > 1 %}
                    <a href="?page={{ current_page - 1 }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        السابق
                    </a>
                    {% else %}
                    <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                        السابق
                    </span>
                    {% endif %}

                    <!-- Page Numbers -->
                    <div class="flex items-center space-x-1 rtl:space-x-reverse">
                        {% set start_page = [1, current_page - 2]|max %}
                        {% set end_page = [total_pages, current_page + 2]|min %}

                        {% if start_page > 1 %}
                        <a href="?page=1{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">1</a>
                        {% if start_page > 2 %}
                        <span class="px-2 py-2 text-sm text-gray-500">...</span>
                        {% endif %}
                        {% endif %}

                        {% for page_num in range(start_page, end_page + 1) %}
                        {% if page_num == current_page %}
                        <span class="px-3 py-2 text-sm font-medium text-white bg-primary-600 border border-primary-600 rounded-md">
                            {{ page_num }}
                        </span>
                        {% else %}
                        <a href="?page={{ page_num }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            {{ page_num }}
                        </a>
                        {% endif %}
                        {% endfor %}

                        {% if end_page < total_pages %}
                        {% if end_page < total_pages - 1 %}
                        <span class="px-2 py-2 text-sm text-gray-500">...</span>
                        {% endif %}
                        <a href="?page={{ total_pages }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ total_pages }}</a>
                        {% endif %}
                    </div>

                    <!-- Next Page -->
                    {% if current_page < total_pages %}
                    <a href="?page={{ current_page + 1 }}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}&per_page={{ per_page }}"
                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        التالي
                    </a>
                    {% else %}
                    <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
                        التالي
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    {% else %}
    <!-- Empty State -->
    <div class="card">
        <div class="card-body text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h4 class="mt-4 text-lg font-medium text-gray-900">لا توجد طلبات</h4>
            {% if current_status %}
            <p class="mt-2 text-sm text-gray-600">لا توجد طلبات بحالة "{{ current_status }}"</p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<script>
// Toggle search section
function toggleSearchSection() {
    const content = document.getElementById('searchSectionContent');
    const quickInfo = document.getElementById('searchQuickInfo');
    const icon = document.getElementById('searchToggleIcon');

    if (content.style.display === 'none') {
        // Show search section
        content.style.display = 'block';
        quickInfo.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
        localStorage.setItem('searchSectionExpanded', 'true');
    } else {
        // Hide search section
        content.style.display = 'none';
        quickInfo.classList.remove('hidden');
        icon.style.transform = 'rotate(-90deg)';
        localStorage.setItem('searchSectionExpanded', 'false');
    }
}

// Initialize search section state on page load
document.addEventListener('DOMContentLoaded', function() {
    const content = document.getElementById('searchSectionContent');
    const quickInfo = document.getElementById('searchQuickInfo');
    const icon = document.getElementById('searchToggleIcon');

    // Check if there are active filters - if so, keep expanded
    const hasActiveFilters = {{ 'true' if current_search or current_status else 'false' }};

    // Get saved state from localStorage, default to collapsed if no active filters
    const isExpanded = hasActiveFilters || localStorage.getItem('searchSectionExpanded') === 'true';

    if (!isExpanded) {
        content.style.display = 'none';
        quickInfo.classList.remove('hidden');
        icon.style.transform = 'rotate(-90deg)';
    } else {
        content.style.display = 'block';
        quickInfo.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
});

function toggleDropdown(requestId, event) {
    // Prevent default behavior and stop propagation
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    const dropdownId = `dropdown-${requestId}`;
    const dropdown = document.getElementById(dropdownId);
    const button = event.target.closest('button');

    // Enhanced error handling
    if (!dropdown || !button) {
        console.warn(`Dropdown elements not found for request ${requestId}`);
        showToast('خطأ في تحميل القائمة', 'error');
        return false;
    }

    // Prevent multiple rapid clicks
    if (button.disabled || button.classList.contains('loading')) {
        return false;
    }

    const isHidden = dropdown.classList.contains('hidden');

    // Close all other dropdowns using global state manager
    if (window.DropdownStateManager) {
        window.DropdownStateManager.closeAll();
    } else {
        // Fallback - close all other dropdowns and reset aria-expanded
        document.querySelectorAll('[id^="dropdown-"]').forEach(d => {
            if (d.id !== dropdownId) {
                d.classList.add('hidden');
                const otherButton = document.querySelector(`[data-request-id="${d.id.replace('dropdown-', '')}"]`);
                if (otherButton) {
                    otherButton.setAttribute('aria-expanded', 'false');
                }
            }
        });
    }

    if (isHidden) {
        // Add loading state
        button.classList.add('loading', 'active');

        try {
            // Position dropdown intelligently
            positionDropdown(dropdown, button);

            // Advanced animation sequence
            dropdown.classList.remove('hidden');
            dropdown.classList.add('dropdown-entering');

            // Force reflow for animation
            dropdown.offsetHeight;

            // Start enter animation
            requestAnimationFrame(() => {
                dropdown.classList.remove('dropdown-entering');
                dropdown.classList.add('dropdown-entered');
            });

            // Update ARIA attributes
            button.setAttribute('aria-expanded', 'true');

            // Focus management with delay for animation
            setTimeout(() => {
                const firstMenuItem = dropdown.querySelector('[role="menuitem"]');
                if (firstMenuItem) {
                    firstMenuItem.focus();
                }
                button.classList.remove('loading');
            }, 150);

            // Add to global state manager
            if (window.DropdownStateManager) {
                window.DropdownStateManager.add(dropdownId);
            }

            // Track active dropdown for performance
            if (window.setActiveDropdown) {
                window.setActiveDropdown(dropdown);
            }

            // Add class to table to disable hover effects and manage z-index
            const table = dropdown.closest('table');
            const tableRow = dropdown.closest('tr');
            const tableCell = dropdown.closest('td');
            const dropdownContainer = dropdown.closest('.table-dropdown');

            if (table) {
                table.classList.add('dropdown-open');
            }

            // Ensure proper z-index stacking for the active dropdown
            if (tableRow) {
                tableRow.style.zIndex = '1000';
                tableRow.style.position = 'relative';
            }
            if (tableCell) {
                tableCell.style.zIndex = '1000';
                tableCell.style.position = 'relative';
            }
            if (dropdownContainer) {
                dropdownContainer.style.zIndex = '1001';
                dropdownContainer.style.position = 'relative';
            }

            // Add keyboard navigation
            addKeyboardNavigation(dropdown, button);

            // Analytics tracking (optional)
            if (window.trackEvent) {
                window.trackEvent('dropdown_opened', { requestId });
            }

        } catch (error) {
            console.error('Error opening dropdown:', error);
            button.classList.remove('loading', 'active');
            showToast('خطأ في فتح القائمة', 'error');
            return false;
        }

    } else {
        // Advanced closing animation
        closeDropdownWithAnimation(dropdown, button, dropdownId);
    }

    // Prevent page scrolling
    return false;
}

// Advanced dropdown closing with animation
function closeDropdownWithAnimation(dropdown, button, dropdownId) {
    return new Promise((resolve) => {
        try {
            // Start exit animation
            dropdown.classList.remove('dropdown-entered');
            dropdown.classList.add('dropdown-exiting');

            // Update button state
            button.classList.remove('active');
            button.setAttribute('aria-expanded', 'false');

            // Animation complete handler
            const handleAnimationComplete = () => {
                dropdown.classList.add('hidden');
                dropdown.classList.remove('dropdown-exiting');

                // Return focus to button
                if (button && typeof button.focus === 'function') {
                    button.focus();
                }

                // Remove from global state manager
                if (window.DropdownStateManager) {
                    window.DropdownStateManager.remove(dropdownId);
                }

                // Clear active dropdown tracking
                if (window.clearActiveDropdown) {
                    window.clearActiveDropdown();
                }

                // Remove class from table to re-enable hover effects and reset z-index
                const table = dropdown.closest('table');
                const tableRow = dropdown.closest('tr');
                const tableCell = dropdown.closest('td');
                const dropdownContainer = dropdown.closest('.table-dropdown');

                if (table) {
                    table.classList.remove('dropdown-open');
                }

                // Reset z-index values to default
                if (tableRow) {
                    tableRow.style.zIndex = '';
                    tableRow.style.position = '';
                }
                if (tableCell) {
                    tableCell.style.zIndex = '';
                    tableCell.style.position = '';
                }
                if (dropdownContainer) {
                    dropdownContainer.style.zIndex = '';
                    dropdownContainer.style.position = '';
                }

                // Remove keyboard navigation
                removeKeyboardNavigation(dropdown);

                // Analytics tracking (optional)
                if (window.trackEvent) {
                    window.trackEvent('dropdown_closed', { dropdownId });
                }

                resolve();
            };

            // Use timeout as fallback for animation completion
            setTimeout(handleAnimationComplete, 200);

        } catch (error) {
            console.error('Error closing dropdown:', error);
            // Fallback: immediate close
            dropdown.classList.add('hidden');
            dropdown.classList.remove('dropdown-entered', 'dropdown-exiting');
            button.classList.remove('active');
            button.setAttribute('aria-expanded', 'false');
            resolve();
        }
    });
}

function positionDropdown(dropdown, button) {
    // Reset any custom positioning
    dropdown.style.left = '';
    dropdown.style.right = '';
    dropdown.style.top = '';
    dropdown.style.bottom = '';
    dropdown.style.transform = '';

    // Get button position relative to viewport
    const buttonRect = button.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Always use fixed positioning to avoid table overflow issues
    dropdown.style.position = 'fixed';
    dropdown.style.zIndex = '999999';
    dropdown.style.backgroundColor = '#ffffff';
    dropdown.style.background = '#ffffff';
    dropdown.style.opacity = '1';
    dropdown.style.visibility = 'visible';

    // Smart dropdown dimensions based on content and viewport
    const baseWidth = viewportWidth < 768 ? 240 : 256;
    const maxWidth = Math.min(baseWidth, viewportWidth - 40);
    const dropdownWidth = maxWidth;

    // Measure actual dropdown height
    dropdown.style.visibility = 'hidden';
    dropdown.style.display = 'block';
    const dropdownHeight = Math.min(dropdown.offsetHeight, viewportHeight - 100);
    dropdown.style.display = '';
    dropdown.style.visibility = 'visible';

    // Smart horizontal positioning with collision detection
    let leftPosition;
    let horizontalAlignment = 'left'; // Track alignment for animation origin

    if (viewportWidth < 768) {
        // Mobile: center relative to button with collision detection
        const buttonCenter = buttonRect.left + (buttonRect.width / 2);
        const centeredPosition = buttonCenter - (dropdownWidth / 2);

        // Check for left edge collision
        if (centeredPosition < 20) {
            leftPosition = 20;
            horizontalAlignment = 'left';
        }
        // Check for right edge collision
        else if (centeredPosition + dropdownWidth > viewportWidth - 20) {
            leftPosition = viewportWidth - dropdownWidth - 20;
            horizontalAlignment = 'right';
        }
        // No collision, use centered position
        else {
            leftPosition = centeredPosition;
            horizontalAlignment = 'center';
        }
    } else {
        // Desktop: intelligent positioning with multiple fallbacks
        const spaceOnRight = viewportWidth - buttonRect.right;
        const spaceOnLeft = buttonRect.left;
        const margin = 20;

        // Primary: align to left edge of button
        if (spaceOnRight >= dropdownWidth + margin) {
            leftPosition = buttonRect.left;
            horizontalAlignment = 'left';
        }
        // Secondary: align to right edge of button
        else if (spaceOnLeft >= dropdownWidth + margin) {
            leftPosition = buttonRect.right - dropdownWidth;
            horizontalAlignment = 'right';
        }
        // Tertiary: center on button
        else {
            const buttonCenter = buttonRect.left + (buttonRect.width / 2);
            leftPosition = Math.max(margin, Math.min(buttonCenter - (dropdownWidth / 2), viewportWidth - dropdownWidth - margin));
            horizontalAlignment = 'center';
        }
    }

    // Set transform origin based on alignment
    const originMap = {
        'left': 'top left',
        'right': 'top right',
        'center': 'top center'
    };
    dropdown.style.transformOrigin = originMap[horizontalAlignment] || 'top center';

    dropdown.style.left = leftPosition + 'px';
    dropdown.style.width = dropdownWidth + 'px';

    // Smart vertical positioning with collision detection
    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;
    const margin = 10;
    let verticalPosition = 'below';

    // Determine optimal vertical position
    if (spaceBelow >= dropdownHeight + margin) {
        // Enough space below
        dropdown.style.top = (buttonRect.bottom + 8) + 'px';
        dropdown.style.bottom = 'auto';
        verticalPosition = 'below';
    } else if (spaceAbove >= dropdownHeight + margin) {
        // Not enough space below, but enough above
        dropdown.style.bottom = (viewportHeight - buttonRect.top + 8) + 'px';
        dropdown.style.top = 'auto';
        verticalPosition = 'above';
        // Update transform origin for upward animation
        dropdown.style.transformOrigin = dropdown.style.transformOrigin.replace('top', 'bottom');
    } else {
        // Limited space both above and below - use the larger space
        if (spaceBelow > spaceAbove) {
            // Position below with constrained height
            dropdown.style.top = (buttonRect.bottom + 8) + 'px';
            dropdown.style.bottom = margin + 'px';
            dropdown.style.maxHeight = (spaceBelow - margin - 8) + 'px';
            dropdown.style.overflowY = 'auto';
            verticalPosition = 'below-constrained';
        } else {
            // Position above with constrained height
            dropdown.style.bottom = (viewportHeight - buttonRect.top + 8) + 'px';
            dropdown.style.top = margin + 'px';
            dropdown.style.maxHeight = (spaceAbove - margin - 8) + 'px';
            dropdown.style.overflowY = 'auto';
            verticalPosition = 'above-constrained';
            dropdown.style.transformOrigin = dropdown.style.transformOrigin.replace('top', 'bottom');
        }
    }

    // Add positioning class for CSS targeting
    dropdown.classList.add(`positioned-${verticalPosition}`);

    // Store positioning info for potential repositioning
    dropdown._positionInfo = {
        horizontal: horizontalAlignment,
        vertical: verticalPosition,
        buttonRect: buttonRect,
        viewportWidth: viewportWidth,
        viewportHeight: viewportHeight
    };

    // Ensure dropdown is visible
    dropdown.style.visibility = 'visible';
    dropdown.style.opacity = '1';
}

// Advanced keyboard navigation for dropdown menus
function addKeyboardNavigation(dropdown, button) {
    const menuItems = dropdown.querySelectorAll('[role="menuitem"]');
    let currentIndex = -1;
    let searchString = '';
    let searchTimeout;

    function focusMenuItem(index, smooth = false) {
        if (index >= 0 && index < menuItems.length) {
            // Remove previous focus styling
            menuItems.forEach(item => item.classList.remove('keyboard-focused'));

            // Add focus styling and focus element
            menuItems[index].classList.add('keyboard-focused');
            menuItems[index].focus();
            currentIndex = index;

            // Smooth scroll into view if needed
            if (smooth) {
                menuItems[index].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }
        }
    }

    function handleTypeaheadSearch(char) {
        // Clear previous search timeout
        clearTimeout(searchTimeout);

        // Add character to search string
        searchString += char.toLowerCase();

        // Find matching menu item
        const matchingIndex = Array.from(menuItems).findIndex((item, index) => {
            const text = item.textContent.trim().toLowerCase();
            return index > currentIndex && text.startsWith(searchString);
        });

        // If no match found after current index, search from beginning
        const fallbackIndex = matchingIndex === -1 ?
            Array.from(menuItems).findIndex(item =>
                item.textContent.trim().toLowerCase().startsWith(searchString)
            ) : matchingIndex;

        if (fallbackIndex !== -1) {
            focusMenuItem(fallbackIndex, true);
        }

        // Clear search string after delay
        searchTimeout = setTimeout(() => {
            searchString = '';
        }, 1000);
    }

    function handleKeyDown(event) {
        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                currentIndex = (currentIndex + 1) % menuItems.length;
                focusMenuItem(currentIndex, true);
                break;
            case 'ArrowUp':
                event.preventDefault();
                currentIndex = currentIndex <= 0 ? menuItems.length - 1 : currentIndex - 1;
                focusMenuItem(currentIndex, true);
                break;
            case 'Home':
                event.preventDefault();
                focusMenuItem(0, true);
                break;
            case 'End':
                event.preventDefault();
                focusMenuItem(menuItems.length - 1, true);
                break;
            case 'Enter':
            case ' ':
                event.preventDefault();
                if (currentIndex >= 0 && menuItems[currentIndex]) {
                    menuItems[currentIndex].click();
                }
                break;
            case 'Escape':
                event.preventDefault();
                closeDropdownWithAnimation(dropdown, button, dropdown.id);
                break;
            case 'Tab':
                // Allow tab to close dropdown and move to next element
                event.preventDefault();
                closeDropdownWithAnimation(dropdown, button, dropdown.id);
                // Focus next focusable element
                setTimeout(() => {
                    const focusableElements = document.querySelectorAll(
                        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                    );
                    const currentButtonIndex = Array.from(focusableElements).indexOf(button);
                    const nextElement = focusableElements[currentButtonIndex + (event.shiftKey ? -1 : 1)];
                    if (nextElement) nextElement.focus();
                }, 100);
                break;
            default:
                // Handle typeahead search for printable characters
                if (event.key.length === 1 && !event.ctrlKey && !event.altKey && !event.metaKey) {
                    event.preventDefault();
                    handleTypeaheadSearch(event.key);
                }
                break;
        }
    }

    // Store the handler for later removal
    dropdown._keydownHandler = handleKeyDown;
    dropdown.addEventListener('keydown', handleKeyDown);

    // Enhanced focus/blur handlers for menu items
    menuItems.forEach((item, index) => {
        item.addEventListener('focus', () => {
            currentIndex = index;
        });

        item.addEventListener('mouseenter', () => {
            // Sync keyboard focus with mouse hover
            if (currentIndex !== index) {
                focusMenuItem(index);
            }
        });
    });

    // Clear search string when dropdown loses focus
    dropdown.addEventListener('blur', () => {
        clearTimeout(searchTimeout);
        searchString = '';
    });
}

function removeKeyboardNavigation(dropdown) {
    if (dropdown._keydownHandler) {
        dropdown.removeEventListener('keydown', dropdown._keydownHandler);
        delete dropdown._keydownHandler;
    }
}

// Enhanced status change handler with loading states and error handling
function handleStatusChange(event, requestId, newStatus) {
    event.preventDefault();
    event.stopPropagation();

    const button = event.currentTarget;
    const form = button.closest('form');
    const loader = button.querySelector('.status-loader');
    const icon = button.querySelector('i:not(.fa-spinner)');

    // Prevent multiple submissions
    if (button.disabled) return false;

    // Show loading state
    button.disabled = true;
    if (loader) loader.classList.remove('hidden');
    if (icon) icon.style.opacity = '0.5';

    // Status labels for user feedback
    const statusLabels = {
        'pending': 'قيد المراجعة',
        'in_progress': 'قيد التنفيذ',
        'completed': 'مكتمل',
        'rejected': 'مرفوض'
    };

    // Submit form with enhanced error handling
    const formData = new FormData(form);

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text();
    })
    .then(data => {
        // Success feedback
        showToast(`تم تغيير حالة الطلب إلى "${statusLabels[newStatus]}" بنجاح`, 'success');

        // Close dropdown with animation
        const dropdown = button.closest('.dropdown-menu');
        if (dropdown) {
            const dropdownId = dropdown.id;
            const triggerButton = document.querySelector(`[data-request-id="${requestId}"]`);
            closeDropdownWithAnimation(dropdown, triggerButton, dropdownId);
        }

        // Reload page after short delay to show updated status
        setTimeout(() => {
            window.location.reload();
        }, 1500);
    })
    .catch(error => {
        console.error('Status change error:', error);
        showToast('فشل في تغيير حالة الطلب. يرجى المحاولة مرة أخرى.', 'error');

        // Reset button state
        button.disabled = false;
        if (loader) loader.classList.add('hidden');
        if (icon) icon.style.opacity = '1';
    });

    return false;
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        showToast('تم نسخ الرمز بنجاح', 'success');
        console.log('Copied to clipboard: ' + text);
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        showToast('فشل في نسخ الرمز', 'error');
    });
}

// Update request status function
function updateStatus(requestId, status) {
    if (!requestId || !status) {
        showToast('خطأ في البيانات المرسلة', 'error');
        return;
    }

    // Show confirmation dialog
    const statusText = status === 'approved' ? 'قبول' : status === 'rejected' ? 'رفض' : status === 'completed' ? 'إكمال' : 'تحديث';
    const confirmMessage = `هل أنت متأكد من ${statusText} هذا الطلب؟`;

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';

    // Send update request
    fetch(`/admin/requests/${requestId}/update-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `status=${encodeURIComponent(status)}`
    })
    .then(response => {
        if (response.ok) {
            showToast('تم تحديث حالة الطلب بنجاح', 'success');
            // Reload the page to reflect changes
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            throw new Error('Network response was not ok');
        }
    })
    .catch(error => {
        console.error('Error updating status:', error);
        showToast('حدث خطأ أثناء تحديث حالة الطلب', 'error');
        // Restore button state
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => toast.remove());

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    // Set toast styling based on type
    if (type === 'success') {
        toast.className += ' bg-green-500 text-white';
        toast.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
    } else if (type === 'error') {
        toast.className += ' bg-red-500 text-white';
        toast.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
    } else {
        toast.className += ' bg-blue-500 text-white';
        toast.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
    }

    // Add to document
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Enhanced dropdown management with performance optimizations
(function() {
    let isInitialized = false;
    let activeDropdown = null;
    let touchStartY = 0;
    let touchStartTime = 0;

    function closeAllDropdowns() {
        if (window.DropdownStateManager) {
            window.DropdownStateManager.closeAll();
        } else {
            // Optimized fallback - only close if there's an active dropdown
            if (activeDropdown) {
                activeDropdown.classList.add('hidden');
                const button = document.querySelector(`[data-request-id="${activeDropdown.id.replace('dropdown-', '')}"]`);
                if (button) {
                    button.setAttribute('aria-expanded', 'false');
                }

                // Remove dropdown-open class from table and reset z-index
                const table = activeDropdown.closest('table');
                const tableRow = activeDropdown.closest('tr');
                const tableCell = activeDropdown.closest('td');
                const dropdownContainer = activeDropdown.closest('.table-dropdown');

                if (table) {
                    table.classList.remove('dropdown-open');
                }

                // Reset z-index values
                if (tableRow) {
                    tableRow.style.zIndex = '';
                    tableRow.style.position = '';
                }
                if (tableCell) {
                    tableCell.style.zIndex = '';
                    tableCell.style.position = '';
                }
                if (dropdownContainer) {
                    dropdownContainer.style.zIndex = '';
                    dropdownContainer.style.position = '';
                }

                // Remove keyboard navigation
                removeKeyboardNavigation(activeDropdown);
                activeDropdown = null;
            }

            // Fallback for any remaining dropdowns
            document.querySelectorAll('[id^="dropdown-"]:not(.hidden)').forEach(dropdown => {
                dropdown.classList.add('hidden');
                const button = document.querySelector(`[data-request-id="${dropdown.id.replace('dropdown-', '')}"]`);
                if (button) {
                    button.setAttribute('aria-expanded', 'false');
                }

                // Remove dropdown-open class from table and reset z-index
                const table = dropdown.closest('table');
                const tableRow = dropdown.closest('tr');
                const tableCell = dropdown.closest('td');
                const dropdownContainer = dropdown.closest('.table-dropdown');

                if (table) {
                    table.classList.remove('dropdown-open');
                }

                // Reset z-index values
                if (tableRow) {
                    tableRow.style.zIndex = '';
                    tableRow.style.position = '';
                }
                if (tableCell) {
                    tableCell.style.zIndex = '';
                    tableCell.style.position = '';
                }
                if (dropdownContainer) {
                    dropdownContainer.style.zIndex = '';
                    dropdownContainer.style.position = '';
                }

                // Remove keyboard navigation
                removeKeyboardNavigation(dropdown);
            });
        }
    }

    // Track active dropdown for performance
    window.setActiveDropdown = function(dropdown) {
        activeDropdown = dropdown;
    };

    window.clearActiveDropdown = function() {
        activeDropdown = null;
    };

    // Performance monitoring for dropdown operations
    window.DropdownPerformanceMonitor = {
        metrics: {
            openCount: 0,
            closeCount: 0,
            averageOpenTime: 0,
            averageCloseTime: 0,
            errorCount: 0
        },

        startTimer: function() {
            return performance.now();
        },

        recordOpen: function(startTime) {
            const duration = performance.now() - startTime;
            this.metrics.openCount++;
            this.metrics.averageOpenTime =
                (this.metrics.averageOpenTime * (this.metrics.openCount - 1) + duration) / this.metrics.openCount;

            if (duration > 100) {
                console.warn(`Slow dropdown open: ${duration.toFixed(2)}ms`);
            }
        },

        recordClose: function(startTime) {
            const duration = performance.now() - startTime;
            this.metrics.closeCount++;
            this.metrics.averageCloseTime =
                (this.metrics.averageCloseTime * (this.metrics.closeCount - 1) + duration) / this.metrics.closeCount;
        },

        recordError: function(error, context) {
            this.metrics.errorCount++;
            console.error(`Dropdown error in ${context}:`, error);

            // Optional: Send to analytics service
            if (window.trackEvent) {
                window.trackEvent('dropdown_error', {
                    error: error.message,
                    context: context,
                    userAgent: navigator.userAgent
                });
            }
        },

        getReport: function() {
            return {
                ...this.metrics,
                successRate: this.metrics.openCount > 0 ?
                    ((this.metrics.openCount - this.metrics.errorCount) / this.metrics.openCount * 100).toFixed(2) + '%' : 'N/A'
            };
        }
    };

    // Simple event tracking system
    window.trackEvent = window.trackEvent || function(eventName, properties) {
        // Placeholder for analytics integration
        if (window.gtag) {
            window.gtag('event', eventName, properties);
        } else if (window.analytics) {
            window.analytics.track(eventName, properties);
        } else {
            console.log('Event tracked:', eventName, properties);
        }
    };

    // Advanced internationalization system
    window.DropdownI18n = {
        currentLocale: 'ar',
        rtl: true,

        messages: {
            ar: {
                // Status labels
                'status.pending': 'قيد المراجعة',
                'status.in_progress': 'قيد التنفيذ',
                'status.completed': 'مكتمل',
                'status.rejected': 'مرفوض',

                // Actions
                'action.edit': 'تعديل الطلب',
                'action.view': 'عرض التفاصيل',
                'action.copy': 'نسخ الرمز',
                'action.archive': 'أرشفة',
                'action.delete': 'حذف',

                // Messages
                'message.loading': 'جاري التحميل...',
                'message.success': 'تم بنجاح',
                'message.error': 'حدث خطأ',
                'message.confirm': 'هل أنت متأكد؟',
                'message.status_changed': 'تم تغيير حالة الطلب إلى "{status}" بنجاح',
                'message.dropdown_error': 'خطأ في تحميل القائمة',
                'message.status_error': 'فشل في تغيير حالة الطلب. يرجى المحاولة مرة أخرى.',

                // Accessibility
                'a11y.dropdown_help': 'استخدم الأسهم للتنقل، Enter للاختيار، Escape للإغلاق. يمكنك أيضاً كتابة أول حرف من الخيار للانتقال إليه مباشرة.',
                'a11y.button_label': 'إجراءات الطلب رقم {number} - الحالة الحالية: {status}',
                'a11y.menu_opened': 'تم فتح قائمة الإجراءات',
                'a11y.menu_closed': 'تم إغلاق قائمة الإجراءات',
                'a11y.item_focused': 'تم التركيز على {item}',

                // Headers
                'header.actions': 'إجراءات الطلب #{number}',
                'header.status_change': 'تغيير الحالة',
                'header.primary_actions': 'الإجراءات الأساسية'
            },

            en: {
                // Status labels
                'status.pending': 'Pending Review',
                'status.in_progress': 'In Progress',
                'status.completed': 'Completed',
                'status.rejected': 'Rejected',

                // Actions
                'action.edit': 'Edit Request',
                'action.view': 'View Details',
                'action.copy': 'Copy Code',
                'action.archive': 'Archive',
                'action.delete': 'Delete',

                // Messages
                'message.loading': 'Loading...',
                'message.success': 'Success',
                'message.error': 'Error occurred',
                'message.confirm': 'Are you sure?',
                'message.status_changed': 'Request status changed to "{status}" successfully',
                'message.dropdown_error': 'Error loading menu',
                'message.status_error': 'Failed to change request status. Please try again.',

                // Accessibility
                'a11y.dropdown_help': 'Use arrows to navigate, Enter to select, Escape to close. You can also type the first letter of an option to jump to it.',
                'a11y.button_label': 'Actions for request #{number} - Current status: {status}',
                'a11y.menu_opened': 'Actions menu opened',
                'a11y.menu_closed': 'Actions menu closed',
                'a11y.item_focused': 'Focused on {item}',

                // Headers
                'header.actions': 'Request #{number} Actions',
                'header.status_change': 'Change Status',
                'header.primary_actions': 'Primary Actions'
            }
        },

        t: function(key, params = {}) {
            const message = this.messages[this.currentLocale]?.[key] ||
                           this.messages['ar'][key] ||
                           key;

            // Simple template replacement
            return message.replace(/\{(\w+)\}/g, (match, param) => {
                return params[param] || match;
            });
        },

        setLocale: function(locale) {
            this.currentLocale = locale;
            this.rtl = ['ar', 'he', 'fa'].includes(locale);
            document.documentElement.dir = this.rtl ? 'rtl' : 'ltr';
            document.documentElement.lang = locale;
        },

        formatNumber: function(number) {
            if (this.currentLocale === 'ar') {
                // Convert to Arabic-Indic numerals
                return number.toString().replace(/\d/g, d => '٠١٢٣٤٥٦٧٨٩'[d]);
            }
            return number.toString();
        }
    };

    // Advanced dropdown configuration system
    window.DropdownConfig = {
        // Animation settings
        animation: {
            enabled: true,
            duration: 250,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
            staggerDelay: 50,
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
        },

        // Positioning settings
        positioning: {
            preferredDirection: 'auto', // 'auto', 'below', 'above'
            margin: 8,
            viewportPadding: 20,
            constrainToViewport: true,
            repositionOnScroll: true
        },

        // Accessibility settings
        accessibility: {
            announceChanges: true,
            focusManagement: true,
            keyboardNavigation: true,
            typeaheadSearch: true,
            typeaheadTimeout: 1000,
            screenReaderSupport: true
        },

        // Behavior settings
        behavior: {
            closeOnOutsideClick: true,
            closeOnEscape: true,
            closeOnTab: true,
            closeOnScroll: false,
            preventMultipleOpen: true,
            autoFocusFirstItem: true,
            returnFocusOnClose: true
        },

        // Performance settings
        performance: {
            enableMonitoring: true,
            logSlowOperations: true,
            slowOperationThreshold: 100,
            enableAnalytics: true,
            debounceResize: 100,
            debounceScroll: 50
        },

        // Visual settings
        visual: {
            theme: 'auto', // 'light', 'dark', 'auto', 'high-contrast', 'compact'
            showIcons: true,
            showStatusColors: true,
            showLoadingStates: true,
            showTooltips: true,
            enableHoverEffects: true
        },

        // Update configuration
        update: function(newConfig) {
            this.merge(this, newConfig);
            this.applyConfiguration();
        },

        // Merge configurations
        merge: function(target, source) {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = target[key] || {};
                    this.merge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        },

        // Apply configuration changes
        applyConfiguration: function() {
            // Apply theme
            if (this.visual.theme !== 'auto') {
                document.documentElement.setAttribute('data-theme', this.visual.theme);
            } else {
                document.documentElement.removeAttribute('data-theme');
            }

            // Apply reduced motion
            if (this.animation.reducedMotion) {
                this.animation.enabled = false;
                document.documentElement.setAttribute('data-reduced-motion', 'true');
            }

            // Apply RTL support
            if (window.DropdownI18n && window.DropdownI18n.rtl) {
                document.documentElement.setAttribute('dir', 'rtl');
            }

            // Update CSS custom properties
            const root = document.documentElement;
            root.style.setProperty('--dropdown-animation-duration', `${this.animation.duration}ms`);
            root.style.setProperty('--dropdown-animation-easing', this.animation.easing);
        },

        // Get configuration value
        get: function(path) {
            return path.split('.').reduce((obj, key) => obj?.[key], this);
        },

        // Set configuration value
        set: function(path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            const target = keys.reduce((obj, key) => {
                obj[key] = obj[key] || {};
                return obj[key];
            }, this);
            target[lastKey] = value;
            this.applyConfiguration();
        }
    };

    // Initialize configuration
    DropdownConfig.applyConfiguration();

    function handleOutsideClick(event) {
        // Optimized - only check if there's an active dropdown
        if (!activeDropdown) return;

        const clickedButton = event.target.closest('button[onclick*="toggleDropdown"]');
        const clickedDropdown = event.target.closest('[id^="dropdown-"]');

        // Don't close if clicking on the dropdown itself or its toggle button
        if (!clickedDropdown && !clickedButton) {
            closeAllDropdowns();
        }
    }

    // Advanced gesture and touch handling system
    window.DropdownGestureHandler = {
        touchStartX: 0,
        touchStartY: 0,
        touchStartTime: 0,
        touchMoved: false,
        longPressTimer: null,

        // Gesture thresholds
        swipeThreshold: 50,
        longPressThreshold: 500,
        tapThreshold: 10,
        doubleTapThreshold: 300,
        lastTapTime: 0,

        handleTouchStart: function(event) {
            const touch = event.touches[0];
            this.touchStartX = touch.clientX;
            this.touchStartY = touch.clientY;
            this.touchStartTime = Date.now();
            this.touchMoved = false;

            // Start long press detection
            this.longPressTimer = setTimeout(() => {
                this.handleLongPress(event);
            }, this.longPressThreshold);

            // Add visual feedback for touch
            const target = event.target.closest('.dropdown-trigger, .dropdown-item');
            if (target) {
                target.classList.add('touch-active');
            }
        },

        handleTouchMove: function(event) {
            if (!this.touchStartTime) return;

            const touch = event.touches[0];
            const deltaX = Math.abs(touch.clientX - this.touchStartX);
            const deltaY = Math.abs(touch.clientY - this.touchStartY);

            // Mark as moved if significant movement
            if (deltaX > this.tapThreshold || deltaY > this.tapThreshold) {
                this.touchMoved = true;
                clearTimeout(this.longPressTimer);

                // Remove touch feedback
                const activeElements = document.querySelectorAll('.touch-active');
                activeElements.forEach(el => el.classList.remove('touch-active'));
            }
        },

        handleTouchEnd: function(event) {
            const touch = event.changedTouches[0];
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - this.touchStartTime;
            const deltaX = Math.abs(touch.clientX - this.touchStartX);
            const deltaY = Math.abs(touch.clientY - this.touchStartY);

            clearTimeout(this.longPressTimer);

            // Remove touch feedback
            const activeElements = document.querySelectorAll('.touch-active');
            activeElements.forEach(el => el.classList.remove('touch-active'));

            // Handle different gesture types
            if (!this.touchMoved && touchDuration < this.longPressThreshold) {
                this.handleTap(event, touchEndTime);
            } else if (this.touchMoved) {
                this.handleSwipe(event, deltaX, deltaY);
            }

            // Reset state
            this.touchStartTime = 0;
            this.touchMoved = false;
        },

        handleTap: function(event, tapTime) {
            const target = event.target.closest('.dropdown-trigger, .dropdown-item');
            if (!target) {
                // Tap outside - close dropdown
                if (activeDropdown) {
                    closeAllDropdowns();
                }
                return;
            }

            // Check for double tap
            const timeSinceLastTap = tapTime - this.lastTapTime;
            if (timeSinceLastTap < this.doubleTapThreshold) {
                this.handleDoubleTap(event, target);
            } else {
                this.handleSingleTap(event, target);
            }

            this.lastTapTime = tapTime;
        },

        handleSingleTap: function(event, target) {
            // Normal tap behavior - let the click handler manage it
            if (target.classList.contains('dropdown-trigger')) {
                // Trigger button tap
                target.click();
            } else if (target.classList.contains('dropdown-item')) {
                // Menu item tap
                target.click();
            }
        },

        handleDoubleTap: function(event, target) {
            event.preventDefault();

            if (target.classList.contains('dropdown-trigger')) {
                // Double tap on trigger - show quick actions or context menu
                this.showQuickActions(target);
            } else if (target.classList.contains('dropdown-item')) {
                // Double tap on item - execute immediately without confirmation
                const form = target.closest('form');
                if (form) {
                    form.submit();
                }
            }
        },

        handleLongPress: function(event) {
            const target = event.target.closest('.dropdown-trigger, .dropdown-item');
            if (!target) return;

            // Provide haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            if (target.classList.contains('dropdown-trigger')) {
                // Long press on trigger - show context menu
                this.showContextMenu(event, target);
            } else if (target.classList.contains('dropdown-item')) {
                // Long press on item - show item details
                this.showItemDetails(target);
            }
        },

        handleSwipe: function(event, deltaX, deltaY) {
            if (!activeDropdown) return;

            // Vertical swipe to close dropdown
            if (deltaY > this.swipeThreshold) {
                closeAllDropdowns();

                // Announce to screen readers
                if (DropdownConfig.accessibility.announceChanges) {
                    this.announceToScreenReader(DropdownI18n.t('a11y.menu_closed'));
                }
            }

            // Horizontal swipe for navigation (future feature)
            if (deltaX > this.swipeThreshold) {
                // Could implement swipe between different action categories
                console.log('Horizontal swipe detected - future feature');
            }
        },

        showQuickActions: function(trigger) {
            // Show a quick action overlay (future enhancement)
            console.log('Quick actions for:', trigger.dataset.requestId);
        },

        showContextMenu: function(event, trigger) {
            // Show native context menu or custom overlay
            console.log('Context menu for:', trigger.dataset.requestId);
        },

        showItemDetails: function(item) {
            // Show item details in a tooltip or modal
            const itemText = item.textContent.trim();
            console.log('Item details for:', itemText);
        },

        announceToScreenReader: function(message) {
            const announcement = document.createElement('div');
            announcement.setAttribute('aria-live', 'polite');
            announcement.setAttribute('aria-atomic', 'true');
            announcement.className = 'sr-only';
            announcement.textContent = message;
            document.body.appendChild(announcement);

            setTimeout(() => {
                document.body.removeChild(announcement);
            }, 1000);
        }
    };

    // Advanced state management system
    window.DropdownStateManager = {
        state: {
            activeDropdowns: new Set(),
            dropdownHistory: [],
            userPreferences: {},
            sessionData: {},
            performanceMetrics: {}
        },

        // State persistence
        persistence: {
            key: 'cmsvs_dropdown_state',

            save: function() {
                try {
                    const stateToSave = {
                        userPreferences: DropdownStateManager.state.userPreferences,
                        sessionData: DropdownStateManager.state.sessionData,
                        timestamp: Date.now()
                    };
                    localStorage.setItem(this.key, JSON.stringify(stateToSave));
                } catch (error) {
                    console.warn('Failed to save dropdown state:', error);
                }
            },

            load: function() {
                try {
                    const saved = localStorage.getItem(this.key);
                    if (saved) {
                        const parsed = JSON.parse(saved);
                        // Only load if saved within last 24 hours
                        if (Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
                            DropdownStateManager.state.userPreferences = parsed.userPreferences || {};
                            DropdownStateManager.state.sessionData = parsed.sessionData || {};
                        }
                    }
                } catch (error) {
                    console.warn('Failed to load dropdown state:', error);
                }
            },

            clear: function() {
                try {
                    localStorage.removeItem(this.key);
                } catch (error) {
                    console.warn('Failed to clear dropdown state:', error);
                }
            }
        },

        // Add dropdown to active set
        add: function(dropdownId) {
            this.state.activeDropdowns.add(dropdownId);
            this.state.dropdownHistory.push({
                id: dropdownId,
                action: 'opened',
                timestamp: Date.now()
            });

            // Limit history size
            if (this.state.dropdownHistory.length > 100) {
                this.state.dropdownHistory = this.state.dropdownHistory.slice(-50);
            }

            this.notifyStateChange('dropdown_opened', { dropdownId });
        },

        // Remove dropdown from active set
        remove: function(dropdownId) {
            this.state.activeDropdowns.delete(dropdownId);
            this.state.dropdownHistory.push({
                id: dropdownId,
                action: 'closed',
                timestamp: Date.now()
            });

            this.notifyStateChange('dropdown_closed', { dropdownId });
        },

        // Check if dropdown is active
        isActive: function(dropdownId) {
            return this.state.activeDropdowns.has(dropdownId);
        },

        // Get all active dropdowns
        getActive: function() {
            return Array.from(this.state.activeDropdowns);
        },

        // Close all dropdowns
        closeAll: function() {
            const activeIds = this.getActive();
            activeIds.forEach(id => {
                const dropdown = document.getElementById(id);
                if (dropdown) {
                    dropdown.classList.add('hidden');
                    const button = document.querySelector(`[data-request-id="${id.replace('dropdown-', '')}"]`);
                    if (button) {
                        button.setAttribute('aria-expanded', 'false');
                    }
                }
                this.remove(id);
            });
        },

        // User preferences management
        setPreference: function(key, value) {
            this.state.userPreferences[key] = value;
            this.persistence.save();
            this.notifyStateChange('preference_changed', { key, value });
        },

        getPreference: function(key, defaultValue = null) {
            return this.state.userPreferences[key] ?? defaultValue;
        },

        // Session data management
        setSessionData: function(key, value) {
            this.state.sessionData[key] = value;
            this.persistence.save();
        },

        getSessionData: function(key, defaultValue = null) {
            return this.state.sessionData[key] ?? defaultValue;
        },

        // Performance metrics
        recordMetric: function(metric, value) {
            if (!this.state.performanceMetrics[metric]) {
                this.state.performanceMetrics[metric] = [];
            }

            this.state.performanceMetrics[metric].push({
                value: value,
                timestamp: Date.now()
            });

            // Keep only last 100 measurements
            if (this.state.performanceMetrics[metric].length > 100) {
                this.state.performanceMetrics[metric] = this.state.performanceMetrics[metric].slice(-50);
            }
        },

        getMetricStats: function(metric) {
            const measurements = this.state.performanceMetrics[metric];
            if (!measurements || measurements.length === 0) {
                return null;
            }

            const values = measurements.map(m => m.value);
            const sum = values.reduce((a, b) => a + b, 0);
            const avg = sum / values.length;
            const min = Math.min(...values);
            const max = Math.max(...values);

            return { avg, min, max, count: values.length };
        },

        // State change notifications
        listeners: new Set(),

        addListener: function(callback) {
            this.listeners.add(callback);
        },

        removeListener: function(callback) {
            this.listeners.delete(callback);
        },

        notifyStateChange: function(event, data) {
            this.listeners.forEach(callback => {
                try {
                    callback(event, data, this.state);
                } catch (error) {
                    console.error('State change listener error:', error);
                }
            });
        },

        // Debug and monitoring
        getDebugInfo: function() {
            return {
                activeDropdowns: this.getActive(),
                historyLength: this.state.dropdownHistory.length,
                preferences: Object.keys(this.state.userPreferences).length,
                sessionData: Object.keys(this.state.sessionData).length,
                metrics: Object.keys(this.state.performanceMetrics),
                listeners: this.listeners.size
            };
        },

        // Initialize state manager
        init: function() {
            this.persistence.load();

            // Apply saved preferences
            const theme = this.getPreference('theme');
            if (theme && DropdownConfig) {
                DropdownConfig.set('visual.theme', theme);
            }

            const reducedMotion = this.getPreference('reducedMotion');
            if (reducedMotion !== null && DropdownConfig) {
                DropdownConfig.set('animation.enabled', !reducedMotion);
            }

            // Clean up on page unload
            window.addEventListener('beforeunload', () => {
                this.persistence.save();
            });
        }
    };

    // Initialize state manager
    DropdownStateManager.init();

    // Advanced utility functions
    window.DropdownUtils = {
        // Debounce function for performance
        debounce: function(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        },

        // Throttle function for performance
        throttle: function(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        },

        // Get optimal position for dropdown
        getOptimalPosition: function(trigger, dropdown) {
            const triggerRect = trigger.getBoundingClientRect();
            const dropdownRect = dropdown.getBoundingClientRect();
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };

            const positions = {
                below: {
                    top: triggerRect.bottom + 8,
                    left: triggerRect.left,
                    spaceAvailable: viewport.height - triggerRect.bottom - 8
                },
                above: {
                    top: triggerRect.top - dropdownRect.height - 8,
                    left: triggerRect.left,
                    spaceAvailable: triggerRect.top - 8
                },
                right: {
                    top: triggerRect.top,
                    left: triggerRect.right + 8,
                    spaceAvailable: viewport.width - triggerRect.right - 8
                },
                left: {
                    top: triggerRect.top,
                    left: triggerRect.left - dropdownRect.width - 8,
                    spaceAvailable: triggerRect.left - 8
                }
            };

            // Find position with most space
            const bestPosition = Object.entries(positions)
                .sort((a, b) => b[1].spaceAvailable - a[1].spaceAvailable)[0];

            return {
                position: bestPosition[0],
                coordinates: bestPosition[1]
            };
        },

        // Generate unique ID
        generateId: function(prefix = 'dropdown') {
            return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        },

        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // Format date for current locale
        formatDate: function(date, options = {}) {
            const locale = DropdownI18n.currentLocale;
            const defaultOptions = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };

            return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options })
                .format(new Date(date));
        },

        // Copy text to clipboard with fallback
        copyToClipboard: async function(text) {
            try {
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                    return true;
                } else {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    const result = document.execCommand('copy');
                    document.body.removeChild(textArea);
                    return result;
                }
            } catch (error) {
                console.error('Failed to copy text:', error);
                return false;
            }
        },

        // Check if device supports touch
        isTouchDevice: function() {
            return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        },

        // Check if user prefers reduced motion
        prefersReducedMotion: function() {
            return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        },

        // Get device type
        getDeviceType: function() {
            const width = window.innerWidth;
            if (width < 768) return 'mobile';
            if (width < 1024) return 'tablet';
            return 'desktop';
        },

        // Validate email
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Sanitize HTML
        sanitizeHtml: function(html) {
            const div = document.createElement('div');
            div.textContent = html;
            return div.innerHTML;
        },

        // Create toast notification
        showToast: function(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 24px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 999999;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
                word-wrap: break-word;
            `;

            // Set background color based on type
            const colors = {
                success: '#22c55e',
                error: '#ef4444',
                warning: '#eab308',
                info: '#3b82f6'
            };
            toast.style.backgroundColor = colors[type] || colors.info;

            document.body.appendChild(toast);

            // Animate in
            requestAnimationFrame(() => {
                toast.style.transform = 'translateX(0)';
            });

            // Auto remove
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, duration);

            return toast;
        }
    };

    // Advanced developer tools and debug console
    window.DropdownDevTools = {
        enabled: false,

        // Enable developer mode
        enable: function() {
            this.enabled = true;
            console.log('🔧 Dropdown Developer Tools Enabled');
            console.log('Available commands:');
            console.log('- DropdownDevTools.getStats() - Get performance statistics');
            console.log('- DropdownDevTools.getConfig() - Get current configuration');
            console.log('- DropdownDevTools.getState() - Get current state');
            console.log('- DropdownDevTools.testAccessibility() - Run accessibility tests');
            console.log('- DropdownDevTools.benchmark() - Run performance benchmark');
            console.log('- DropdownDevTools.exportData() - Export debug data');

            // Add visual indicator
            this.addDevIndicator();
        },

        // Disable developer mode
        disable: function() {
            this.enabled = false;
            this.removeDevIndicator();
            console.log('🔧 Dropdown Developer Tools Disabled');
        },

        // Add visual developer indicator
        addDevIndicator: function() {
            if (document.getElementById('dropdown-dev-indicator')) return;

            const indicator = document.createElement('div');
            indicator.id = 'dropdown-dev-indicator';
            indicator.innerHTML = '🔧 DEV';
            indicator.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: #ff6b35;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                z-index: 999999;
                cursor: pointer;
                user-select: none;
            `;

            indicator.onclick = () => this.showDebugPanel();
            document.body.appendChild(indicator);
        },

        // Remove visual developer indicator
        removeDevIndicator: function() {
            const indicator = document.getElementById('dropdown-dev-indicator');
            if (indicator) {
                indicator.remove();
            }
        },

        // Get performance statistics
        getStats: function() {
            if (!this.enabled) return null;

            const stats = {
                performance: DropdownPerformanceMonitor.getReport(),
                state: DropdownStateManager.getDebugInfo(),
                config: {
                    animation: DropdownConfig.animation,
                    accessibility: DropdownConfig.accessibility,
                    performance: DropdownConfig.performance
                },
                browser: {
                    userAgent: navigator.userAgent,
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    deviceType: DropdownUtils.getDeviceType(),
                    touchSupport: DropdownUtils.isTouchDevice(),
                    reducedMotion: DropdownUtils.prefersReducedMotion()
                }
            };

            console.table(stats.performance);
            return stats;
        },

        // Get current configuration
        getConfig: function() {
            if (!this.enabled) return null;
            return DropdownConfig;
        },

        // Get current state
        getState: function() {
            if (!this.enabled) return null;
            return DropdownStateManager.state;
        },

        // Run accessibility tests
        testAccessibility: function() {
            if (!this.enabled) return null;

            const results = {
                passed: 0,
                failed: 0,
                warnings: 0,
                tests: []
            };

            // Test 1: ARIA attributes
            const triggers = document.querySelectorAll('.dropdown-trigger');
            triggers.forEach((trigger, index) => {
                const test = {
                    name: `Trigger ${index + 1} ARIA attributes`,
                    element: trigger,
                    status: 'passed',
                    issues: []
                };

                if (!trigger.hasAttribute('aria-label')) {
                    test.status = 'failed';
                    test.issues.push('Missing aria-label');
                }

                if (!trigger.hasAttribute('aria-haspopup')) {
                    test.status = 'failed';
                    test.issues.push('Missing aria-haspopup');
                }

                if (!trigger.hasAttribute('aria-expanded')) {
                    test.status = 'failed';
                    test.issues.push('Missing aria-expanded');
                }

                results.tests.push(test);
                results[test.status]++;
            });

            // Test 2: Keyboard navigation
            const dropdowns = document.querySelectorAll('.dropdown-menu');
            dropdowns.forEach((dropdown, index) => {
                const test = {
                    name: `Dropdown ${index + 1} keyboard navigation`,
                    element: dropdown,
                    status: 'passed',
                    issues: []
                };

                const menuItems = dropdown.querySelectorAll('[role="menuitem"]');
                if (menuItems.length === 0) {
                    test.status = 'failed';
                    test.issues.push('No menu items with role="menuitem"');
                }

                menuItems.forEach((item, itemIndex) => {
                    if (!item.hasAttribute('tabindex')) {
                        test.status = 'warning';
                        test.issues.push(`Menu item ${itemIndex + 1} missing tabindex`);
                    }
                });

                results.tests.push(test);
                results[test.status]++;
            });

            console.log('🔍 Accessibility Test Results:', results);
            return results;
        },

        // Run performance benchmark
        benchmark: function() {
            if (!this.enabled) return null;

            console.log('🚀 Running performance benchmark...');

            const startTime = performance.now();
            const iterations = 100;

            // Benchmark dropdown operations
            for (let i = 0; i < iterations; i++) {
                const mockEvent = { preventDefault: () => {}, stopPropagation: () => {} };
                const trigger = document.querySelector('.dropdown-trigger');
                if (trigger) {
                    // Simulate rapid open/close
                    toggleDropdown(1, mockEvent);
                    closeAllDropdowns();
                }
            }

            const endTime = performance.now();
            const totalTime = endTime - startTime;
            const avgTime = totalTime / iterations;

            const results = {
                iterations: iterations,
                totalTime: totalTime.toFixed(2) + 'ms',
                averageTime: avgTime.toFixed(2) + 'ms',
                operationsPerSecond: (1000 / avgTime).toFixed(2)
            };

            console.log('📊 Benchmark Results:', results);
            return results;
        },

        // Export debug data
        exportData: function() {
            if (!this.enabled) return null;

            const data = {
                timestamp: new Date().toISOString(),
                stats: this.getStats(),
                config: this.getConfig(),
                state: this.getState(),
                accessibility: this.testAccessibility(),
                benchmark: this.benchmark()
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dropdown-debug-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            console.log('📁 Debug data exported');
            return data;
        },

        // Show debug panel
        showDebugPanel: function() {
            if (!this.enabled) return;

            const panel = document.createElement('div');
            panel.id = 'dropdown-debug-panel';
            panel.innerHTML = `
                <div style="background: white; border: 1px solid #ccc; border-radius: 8px; padding: 20px; max-width: 500px; max-height: 400px; overflow-y: auto;">
                    <h3 style="margin: 0 0 15px 0;">🔧 Dropdown Debug Panel</h3>
                    <button onclick="DropdownDevTools.getStats()">Get Stats</button>
                    <button onclick="DropdownDevTools.testAccessibility()">Test A11y</button>
                    <button onclick="DropdownDevTools.benchmark()">Benchmark</button>
                    <button onclick="DropdownDevTools.exportData()">Export Data</button>
                    <button onclick="this.parentElement.parentElement.remove()">Close</button>
                    <div id="debug-output" style="margin-top: 15px; font-family: monospace; font-size: 12px; background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;"></div>
                </div>
            `;

            panel.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 999999;
                background: rgba(0, 0, 0, 0.5);
                padding: 20px;
                border-radius: 8px;
            `;

            document.body.appendChild(panel);
        }
    };

    // Auto-enable dev tools in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        DropdownDevTools.enable();
    }

    function handleScroll() {
        closeAllDropdowns();
    }

    function handleResize() {
        closeAllDropdowns();
    }

    function initializeDropdownListeners() {
        if (isInitialized) return;

        // Add event listeners using global manager if available
        if (window.EventListenerManager) {
            window.EventListenerManager.add(document, 'click', handleOutsideClick);
            window.EventListenerManager.add(document, 'scroll', handleScroll, true);
            window.EventListenerManager.add(window, 'resize', handleResize);

            // Advanced gesture support
            if (DropdownConfig.accessibility.keyboardNavigation) {
                window.EventListenerManager.add(document, 'touchstart', DropdownGestureHandler.handleTouchStart.bind(DropdownGestureHandler), { passive: true });
                window.EventListenerManager.add(document, 'touchmove', DropdownGestureHandler.handleTouchMove.bind(DropdownGestureHandler), { passive: true });
                window.EventListenerManager.add(document, 'touchend', DropdownGestureHandler.handleTouchEnd.bind(DropdownGestureHandler), { passive: true });
            }
        } else {
            // Fallback
            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('scroll', handleScroll, true);
            window.addEventListener('resize', handleResize);

            // Advanced gesture support
            if (DropdownConfig.accessibility.keyboardNavigation) {
                document.addEventListener('touchstart', DropdownGestureHandler.handleTouchStart.bind(DropdownGestureHandler), { passive: true });
                document.addEventListener('touchmove', DropdownGestureHandler.handleTouchMove.bind(DropdownGestureHandler), { passive: true });
                document.addEventListener('touchend', DropdownGestureHandler.handleTouchEnd.bind(DropdownGestureHandler), { passive: true });
            }
        }

        // Initialize state management
        if (window.DropdownStateManager) {
            DropdownStateManager.addListener((event, data) => {
                if (DropdownConfig.performance.enableAnalytics && window.trackEvent) {
                    window.trackEvent(event, data);
                }
            });
        }

        // Initialize performance monitoring
        if (DropdownConfig.performance.enableMonitoring) {
            DropdownPerformanceMonitor.recordMetric('initialization_time', Date.now());
        }

        isInitialized = true;
    }

    function cleanupDropdownListeners() {
        if (!isInitialized) return;

        // Note: If using EventListenerManager, cleanup is handled automatically on page unload
        if (!window.EventListenerManager) {
            // Manual cleanup for fallback
            document.removeEventListener('click', handleOutsideClick);
            document.removeEventListener('scroll', handleScroll, true);
            window.removeEventListener('resize', handleResize);
            document.removeEventListener('touchstart', handleTouchStart);
            document.removeEventListener('touchend', handleTouchEnd);
        }

        isInitialized = false;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDropdownListeners);
    } else {
        initializeDropdownListeners();
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanupDropdownListeners);

    // Expose cleanup function globally for manual cleanup if needed
    window.cleanupAdminDropdowns = cleanupDropdownListeners;
})();

// Prevent scrolling when pressing space or enter on dropdown buttons
document.addEventListener('keydown', function(event) {
    const target = event.target;
    if (target.tagName === 'BUTTON' && target.onclick && target.onclick.toString().includes('toggleDropdown')) {
        if (event.key === ' ' || event.key === 'Enter') {
            event.preventDefault();
            target.click();
        }
    }
});
</script>
{% endblock %}